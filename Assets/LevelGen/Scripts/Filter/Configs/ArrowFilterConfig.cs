using LevelGen.Scripts.Filter.Common;
using LevelGen.Scripts.Filter.Pattern;
using UnityEngine;

namespace LevelGen.Scripts.Filter.Configs
{
    /// <summary>
    /// Config cho Arrow Filter
    /// </summary>
    [CreateAssetMenu(fileName = "ArrowFilter", menuName = "LevelGen/Filters/Arrow Filter")]
    public class ArrowFilterConfig : FilterConfigBase
    {
        [Header("Arrow Parameters")]
        [Range(0.2f, 1.5f)] public float lengthScale = 0.7f;
        [Range(0.1f, 0.8f)] public float headWidthScale = 0.4f;
        [Range(0.05f, 0.4f)] public float shaftWidthScale = 0.15f;
        [Range(0.1f, 0.6f)] public float headLengthScale = 0.3f;
        public bool fillInside = true;
        [Range(0f, 1f)] public float centerX = 0.5f;
        [Range(0f, 1f)] public float centerY = 0.5f;
        [Range(0f, 360f)] public float rotation = 0f;

        public override string GetFilterName()
        {
            return "Arrow Filter";
        }

        public override IFilter CreateFilter()
        {
            return new ArrowFilter(lengthScale, headWidthScale, shaftWidthScale, headLengthScale, 
                fillInside, centerX, centerY, rotation);
        }
        
        protected override IFilter CreateRandomFilterInternal()
        {
            return new ArrowFilter(
                UnityEngine.Random.Range(0.4f, 1.2f), // lengthScale
                UnityEngine.Random.Range(0.2f, 0.6f), // headWidthScale
                UnityEngine.Random.Range(0.08f, 0.3f), // shaftWidthScale
                UnityEngine.Random.Range(0.15f, 0.5f), // headLengthScale
                RandomBool(0.8f), // fillInside (80% chance)
                MidCenter().x, // centerX
                MidCenter().y, // centerY
                RandomRotation() // rotation
            );
        }
    }
}
