using LevelGen.Scripts.Filter.Common;
using LevelGen.Scripts.Filter.Pattern;
using UnityEngine;

namespace LevelGen.Scripts.Filter.Configs
{
    /// <summary>
    /// Config cho Checkerboard Filter
    /// </summary>
    [CreateAssetMenu(fileName = "CheckerboardFilter", menuName = "LevelGen/Filters/Checkerboard Filter")]
    public class CheckerboardFilterConfig : FilterConfigBase
    {
        [Header("Checkerboard Parameters")]
        [Range(1, 20)] public int squareSize = 4;
        public bool invertPattern = false;
        [Range(-10, 10)] public int offsetX = 0;
        [Range(-10, 10)] public int offsetY = 0;

        public override string GetFilterName()
        {
            return "Checkerboard Filter";
        }

        public override IFilter CreateFilter()
        {
            return new CheckerboardFilter(squareSize, invertPattern, offsetX, offsetY);
        }
        
        protected override IFilter CreateRandomFilterInternal()
        {
            return new CheckerboardFilter(
                UnityEngine.Random.Range(2, 12), // squareSize
                RandomBool(), // invertPattern
                UnityEngine.Random.Range(-5, 6), // offsetX
                UnityEngine.Random.Range(-5, 6)  // offsetY
            );
        }
    }
}
