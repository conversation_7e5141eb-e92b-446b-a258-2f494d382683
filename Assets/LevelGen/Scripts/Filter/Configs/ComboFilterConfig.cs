using System.Collections.Generic;
using LevelGen.Scripts.Filter.Common;
using UnityEngine;
using Sirenix.OdinInspector;

namespace LevelGen.Scripts.Filter.Configs
{
    /// <summary>
    /// Config cho Combo Filter
    /// </summary>
    [CreateAssetMenu(fileName = "ComboFilter", menuName = "LevelGen/Filters/Combo Filter")]
    public class ComboFilterConfig : FilterConfigBase
    {
        [Header("Combo Parameters")]
        public ComboFilter.ComboMode mode = ComboFilter.ComboMode.Sequential;
        
        [Header("Filter Configs")]
        [InfoBox("Add filter configs that will be combined")]
        [SerializeField] private List<FilterConfigBase> filterConfigs = new List<FilterConfigBase>();

        public override string GetFilterName()
        {
            return "Combo Filter";
        }

        public override IFilter CreateFilter()
        {
            var filters = new List<IFilter>();
            
            foreach (var config in filterConfigs)
            {
                if (config != null)
                {
                    filters.Add(config.CreateFilter());
                }
            }
            
            return new ComboFilter(filters.ToArray(), mode);
        }
        
        protected override IFilter CreateRandomFilterInternal()
        {
            // For random generation, create a simple combo with 2-3 basic filters
            var filters = new List<IFilter>();
            
            // Add some random basic filters
            int filterCount = UnityEngine.Random.Range(2, 4);
            var modes = System.Enum.GetValues(typeof(ComboFilter.ComboMode));
            var randomMode = (ComboFilter.ComboMode)modes.GetValue(UnityEngine.Random.Range(0, modes.Length));
            
            // Create some simple random filters for the combo
            for (int i = 0; i < filterCount; i++)
            {
                // You can add logic here to create random filters
                // For now, we'll just return an empty combo
            }
            
            return new ComboFilter(filters.ToArray(), randomMode);
        }
        
        [Button("Add Filter Config")]
        public void AddFilterConfig(FilterConfigBase config)
        {
            if (config != null && !filterConfigs.Contains(config))
            {
                filterConfigs.Add(config);
            }
        }
        
        [Button("Remove Filter Config")]
        public void RemoveFilterConfig(FilterConfigBase config)
        {
            filterConfigs.Remove(config);
        }
        
        [Button("Clear All Filters")]
        public void ClearAllFilters()
        {
            filterConfigs.Clear();
        }
        
        public List<FilterConfigBase> GetFilterConfigs()
        {
            return new List<FilterConfigBase>(filterConfigs);
        }
    }
}
