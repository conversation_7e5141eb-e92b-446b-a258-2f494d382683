using LevelGen.Scripts.Filter.Common;
using UnityEngine;

namespace LevelGen.Scripts.Filter.Configs
{
    /// <summary>
    /// Config cho Cross Filter
    /// </summary>
    [CreateAssetMenu(fileName = "CrossFilter", menuName = "LevelGen/Filters/Cross Filter")]
    public class CrossFilterConfigBase : FilterConfigBase
    {
        [Header("Cross Parameters")]
        [Range(0.05f, 0.5f)] public float horizontalWidthScale = 0.1f;
        [Range(0.05f, 0.5f)] public float verticalWidthScale = 0.1f;
        [Range(0.2f, 2f)] public float horizontalLengthScale = 0.8f;
        [Range(0.2f, 2f)] public float verticalLengthScale = 0.8f;
        public bool fillInside = true;
        [Range(0f, 1f)] public float centerX = 0.5f;
        [Range(0f, 1f)] public float centerY = 0.5f;
        [Range(0f, 360f)] public float rotation = 0f;

        public override string GetFilterName()
        {
            return "Cross Filter";
        }

        public override IFilter CreateFilter()
        {
            return new CrossFilter(horizontalWidthScale, verticalWidthScale, horizontalLengthScale, verticalLengthScale,
                fillInside, centerX, centerY, rotation);
        }
        
        protected override IFilter CreateRandomFilterInternal()
        {
            return new CrossFilter(
                UnityEngine.Random.Range(0.05f, 0.3f), // horizontalWidthScale
                UnityEngine.Random.Range(0.05f, 0.3f), // verticalWidthScale
                RandomScale(0.4f, 1.5f), // horizontalLengthScale
                RandomScale(0.4f, 1.5f), // verticalLengthScale
                RandomBool(0.8f), // fillInside
                MidCenter().x, // centerX
                MidCenter().y, // centerY
                RandomRotation() // rotation
            );
        }
    }
}
