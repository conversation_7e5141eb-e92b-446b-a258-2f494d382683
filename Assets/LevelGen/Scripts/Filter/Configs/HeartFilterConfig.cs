using LevelGen.Scripts.Filter.Common;
using LevelGen.Scripts.Filter.Pattern;
using UnityEngine;

namespace LevelGen.Scripts.Filter.Configs
{
    /// <summary>
    /// Config cho Heart Filter
    /// </summary>
    [CreateAssetMenu(fileName = "HeartFilter", menuName = "LevelGen/Filters/Heart Filter")]
    public class HeartFilterConfigBase : FilterConfigBase
    {
        [Header("Heart Parameters")]
        [Range(0.1f, 1f)] public float sizeScale = 0.3f;
        public bool fillInside = true;
        [Range(0f, 1f)] public float centerX = 0.5f;
        [Range(0f, 1f)] public float centerY = 0.6f;

        public override string GetFilterName()
        {
            return "Heart Filter";
        }

        public override IFilter CreateFilter()
        {
            return new HeartFilter(sizeScale, fillInside, centerX, centerY);
        }
        
        protected override IFilter CreateRandomFilterInternal()
        {
            return new HeartFilter(
                RandomScale(0.2f, 0.6f), // sizeScale
                RandomBool(0.8f), // fillInside
                MidCenter().x, // centerX
                MidCenter().y // centerY
            );
        }
    }
}
