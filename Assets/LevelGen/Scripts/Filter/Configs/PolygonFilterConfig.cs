using LevelGen.Scripts.Filter.Common;
using LevelGen.Scripts.Filter.Pattern;
using UnityEngine;

namespace LevelGen.Scripts.Filter.Configs
{
    /// <summary>
    /// Config cho Polygon Filter
    /// </summary>
    [CreateAssetMenu(fileName = "PolygonFilter", menuName = "LevelGen/Filters/Polygon Filter")]
    public class PolygonFilterConfigBase : FilterConfigBase
    {
        [Header("Polygon Parameters")]
        [Range(3, 12)] public int sides = 6;
        [Range(0.1f, 2f)] public float radiusScale = 0.4f;
        public bool fillInside = true;
        [Range(0f, 1f)] public float centerX = 0.5f;
        [Range(0f, 1f)] public float centerY = 0.5f;
        [Range(0f, 360f)] public float rotation = 0f;

        public override string GetFilterName()
        {
            return "Polygon Filter";
        }

        public override IFilter CreateFilter()
        {
            return new PolygonFilter(sides, radiusScale, fillInside, centerX, centerY, rotation);
        }
        
        protected override IFilter CreateRandomFilterInternal()
        {
            return new PolygonFilter(
                UnityEngine.Random.Range(3, 10), // sides
                RandomScale(0.3f, 0.8f), // radiusScale
                RandomBool(0.8f), // fillInside
                MidCenter().x, // centerX
                MidCenter().y, // centerY
                RandomRotation() // rotation
            );
        }
    }
}
