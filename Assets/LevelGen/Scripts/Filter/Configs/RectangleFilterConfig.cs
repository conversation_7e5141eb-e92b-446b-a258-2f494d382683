using LevelGen.Scripts.Filter.Common;
using LevelGen.Scripts.Filter.Pattern;
using UnityEngine;

namespace LevelGen.Scripts.Filter.Configs
{
    /// <summary>
    /// Config cho Rectangle Filter
    /// </summary>
    [CreateAssetMenu(fileName = "RectangleFilter", menuName = "LevelGen/Filters/Rectangle Filter")]
    public class RectangleFilterConfigBase : FilterConfigBase
    {
        [Header("Rectangle Parameters")]
        [Range(0.1f, 2f)] public float widthScale = 0.6f;
        [Range(0.1f, 2f)] public float heightScale = 0.4f;
        public bool fillInside = true;
        [Range(0f, 1f)] public float centerX = 0.5f;
        [Range(0f, 1f)] public float centerY = 0.5f;
        public bool roundedCorners = false;
        [Range(0.05f, 0.5f)] public float cornerRadiusScale = 0.1f;

        public override string GetFilterName()
        {
            return "Rectangle Filter";
        }

        public override IFilter CreateFilter()
        {
            return new RectangleFilter(widthScale, heightScale, fillInside, centerX, centerY, roundedCorners, cornerRadiusScale);
        }
        
        protected override IFilter CreateRandomFilterInternal()
        {
            return new RectangleFilter(
                RandomScale(0.3f, 1.5f), // widthScale
                RandomScale(0.3f, 1.5f), // heightScale
                RandomBool(0.8f), // fillInside
                MidCenter().x, // centerX
                MidCenter().y, // centerY
                RandomCorner(),
                RandomCornerRadius() // rotation
            );
        }
        
        bool RandomCorner()
        {
            return UnityEngine.Random.value < 0.5f;
        }
        
        float RandomCornerRadius()
        {
            return UnityEngine.Random.Range(0.05f, 0.5f);
        }
    }
}
