using LevelGen.Scripts.Filter.Common;
using LevelGen.Scripts.Filter.Pattern;
using UnityEngine;

namespace LevelGen.Scripts.Filter.Configs
{
    /// <summary>
    /// Config cho Triangle Filter
    /// </summary>
    [CreateAssetMenu(fileName = "TriangleFilter", menuName = "LevelGen/Filters/Triangle Filter")]
    public class TriangleFilterConfigBase : FilterConfigBase
    {
        [Header("Triangle Parameters")]
        [Range(0.1f, 2f)] public float sizeScale = 0.6f;
        public bool fillInside = true;
        [Range(0f, 1f)] public float centerX = 0.5f;
        [Range(0f, 1f)] public float centerY = 0.5f;
        [Range(0f, 360f)] public float rotation = 0f;
        public TriangleFilter.TriangleType triangleType = TriangleFilter.TriangleType.Equilateral;

        public override string GetFilterName()
        {
            return "Triangle Filter";
        }

        public override IFilter CreateFilter()
        {
            return new TriangleFilter(sizeScale, fillInside, centerX, centerY, rotation, triangleType);
        }
        
        protected override IFilter CreateRandomFilterInternal()
        {
            var triangleTypes = System.Enum.GetValues(typeof(TriangleFilter.TriangleType));
            return new TriangleFilter(
                RandomScale(0.3f, 1.2f), // sizeScale
                RandomBool(0.8f), // fillInside
                MidCenter().x, // centerX
                MidCenter().y, // centerY
                RandomRotation(), // rotation
                (TriangleFilter.TriangleType)triangleTypes.GetValue(UnityEngine.Random.Range(0, triangleTypes.Length))
            );
        }
    }
}
