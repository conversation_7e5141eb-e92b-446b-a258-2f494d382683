using LevelGen.Scripts.Filter.Common;
using UnityEngine;

namespace LevelGen.Scripts.Filter.Pattern
{
    /// <summary>
    /// Filter tạo hình bướm
    /// </summary>
    public class ButterflyFilter : IFilter
    {
        private readonly float wingSpanScale;
        private readonly float wingHeightScale;
        private readonly float bodyWidthScale;
        private readonly float bodyLengthScale;
        private readonly bool fillInside;
        private readonly float centerX;
        private readonly float centerY;
        private readonly float rotation;

        public ButterflyFilter(float wingSpanScale = 0.8f, float wingHeightScale = 0.6f, float bodyWidthScale = 0.08f, 
            float bodyLengthScale = 0.7f, bool fillInside = true, float centerX = 0.5f, float centerY = 0.5f, float rotation = 0f)
        {
            this.wingSpanScale = wingSpanScale;
            this.wingHeightScale = wingHeightScale;
            this.bodyWidthScale = bodyWidthScale;
            this.bodyLengthScale = bodyLengthScale;
            this.fillInside = fillInside;
            this.centerX = centerX;
            this.centerY = centerY;
            this.rotation = rotation;
        }

        public T[,] Filter<T>(T[,] matrix) where T : struct
        {
            int rows = matrix.GetLength(0);
            int cols = matrix.GetLength(1);
            T[,] result = new T[rows, cols];

            float centerRow = (rows - 1) * centerY;
            float centerCol = (cols - 1) * centerX;
            float minSize = Mathf.Min(rows, cols);

            for (int i = 0; i < rows; i++)
            {
                for (int j = 0; j < cols; j++)
                {
                    Vector2 point = TransformPoint(j, i, centerCol, centerRow, rotation);
                    bool isInside = IsInsideButterfly(point, minSize);

                    if ((fillInside && isInside) || (!fillInside && !isInside))
                    {
                        result[i, j] = matrix[i, j];
                    }
                    else
                    {
                        result[i, j] = default(T);
                    }
                }
            }

            return result;
        }

        private Vector2 TransformPoint(float x, float y, float centerX, float centerY, float rotation)
        {
            float translatedX = x - centerX;
            float translatedY = y - centerY;

            float rotRad = -rotation * Mathf.Deg2Rad;
            float rotatedX = translatedX * Mathf.Cos(rotRad) - translatedY * Mathf.Sin(rotRad);
            float rotatedY = translatedX * Mathf.Sin(rotRad) + translatedY * Mathf.Cos(rotRad);

            return new Vector2(rotatedX, rotatedY);
        }

        private bool IsInsideButterfly(Vector2 point, float minSize)
        {
            float x = point.x;
            float y = point.y;

            // Với matrix siêu nhỏ, điều chỉnh scale
            float wingSpan = Mathf.Max(1.2f, minSize * wingSpanScale * 0.4f);
            float wingHeight = Mathf.Max(1f, minSize * wingHeightScale * 0.4f);
            float bodyWidth = Mathf.Max(0.4f, minSize * bodyWidthScale * 0.3f);
            float bodyLength = Mathf.Max(1f, minSize * bodyLengthScale * 0.4f);

            // Check body (vertical ellipse in center)
            if (Mathf.Abs(x) <= bodyWidth && Mathf.Abs(y) <= bodyLength)
                return true;

            // Check wings (only if outside body area)
            if (Mathf.Abs(x) > bodyWidth * 0.5f)
            {
                // Upper wings
                if (y < 0 && y >= -wingHeight * 0.8f)
                {
                    float wingX = Mathf.Abs(x);
                    float wingY = -y; // Make y positive for upper wing

                    // Wing shape: modified ellipse that's wider at the top
                    float normalizedY = wingY / (wingHeight * 0.8f);
                    float wingWidthAtY = wingSpan * (1f - normalizedY * normalizedY * 0.6f);

                    if (wingX <= wingWidthAtY)
                        return true;
                }

                // Lower wings (smaller)
                if (y > 0 && y <= wingHeight * 0.6f)
                {
                    float wingX = Mathf.Abs(x);
                    float wingY = y;

                    // Lower wing shape: smaller ellipse
                    float normalizedY = wingY / (wingHeight * 0.6f);
                    float wingWidthAtY = wingSpan * 0.6f * (1f - normalizedY * normalizedY);

                    if (wingX <= wingWidthAtY)
                        return true;
                }
            }

            return false;
        }
    }
}
