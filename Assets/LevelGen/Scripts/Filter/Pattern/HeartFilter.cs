using LevelGen.Scripts.Filter.Common;
using UnityEngine;

namespace LevelGen.Scripts.Filter.Pattern
{
    /// <summary>
    /// Filter tạo hình trái tim
    /// </summary>
    public class HeartFilter : IFilter
    {
        private readonly float sizeScale;
        private readonly bool fillInside;
        private readonly float centerX;
        private readonly float centerY;

        public HeartFilter(float sizeScale = 0.3f, bool fillInside = true, float centerX = 0.5f, float centerY = 0.6f)
        {
            this.sizeScale = sizeScale;
            this.fillInside = fillInside;
            this.centerX = centerX;
            this.centerY = centerY;
        }

        public T[,] Filter<T>(T[,] matrix) where T : struct
        {
            int rows = matrix.GetLength(0);
            int cols = matrix.GetLength(1);
            T[,] result = new T[rows, cols];

            float centerRow = centerY * (rows - 1);
            float centerCol = centerX * (cols - 1);

            // Với matrix siêu nhỏ, scale cần được điều chỉnh đặc biệt
            float minSize = Mathf.Min(rows, cols);
            float scale = Mathf.Max(1.8f, minSize * sizeScale * 0.8f);

            for (int i = 0; i < rows; i++)
            {
                for (int j = 0; j < cols; j++)
                {
                    // Chuyển đổi tọa độ về hệ tọa độ trái tim
                    float x = (j - centerCol) / scale;
                    float y = -(i - centerRow) / scale; // Đảo ngược y để trái tim hướng lên

                    bool isInside = IsInsideHeart(x, y);

                    if ((fillInside && isInside) || (!fillInside && !isInside))
                    {
                        result[i, j] = matrix[i, j];
                    }
                    else
                    {
                        result[i, j] = default(T);
                    }
                }
            }

            return result;
        }

        private bool IsInsideHeart(float x, float y)
        {
            // Phương trình trái tim: (x^2 + y^2 - 1)^3 - x^2*y^3 <= 0
            // Điều chỉnh để có hình dạng đẹp hơn
            float x2 = x * x;
            float y2 = y * y;
            float y3 = y2 * y;
            
            float heartEquation = (x2 + y2 - 1) * (x2 + y2 - 1) * (x2 + y2 - 1) - x2 * y3;
            
            return heartEquation <= 0;
        }
    }
}
